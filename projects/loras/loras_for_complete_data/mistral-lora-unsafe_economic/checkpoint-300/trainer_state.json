{"best_metric": null, "best_model_checkpoint": null, "epoch": 0.3669724770642202, "eval_steps": 500, "global_step": 300, "is_hyper_param_search": false, "is_local_process_zero": true, "is_world_process_zero": true, "log_history": [{"epoch": 0.0012232415902140672, "grad_norm": 5.089275360107422, "learning_rate": 0.0001999388004895961, "loss": 2.1471, "step": 1}, {"epoch": 0.012232415902140673, "grad_norm": 1.4238688945770264, "learning_rate": 0.00019938800489596083, "loss": 1.6631, "step": 10}, {"epoch": 0.024464831804281346, "grad_norm": 1.521321415901184, "learning_rate": 0.00019877600979192168, "loss": 1.5837, "step": 20}, {"epoch": 0.03669724770642202, "grad_norm": 1.2919552326202393, "learning_rate": 0.0001981640146878825, "loss": 1.5594, "step": 30}, {"epoch": 0.04892966360856269, "grad_norm": 1.3141014575958252, "learning_rate": 0.00019755201958384332, "loss": 1.5343, "step": 40}, {"epoch": 0.06116207951070336, "grad_norm": 1.2899436950683594, "learning_rate": 0.00019694002447980416, "loss": 1.5094, "step": 50}, {"epoch": 0.07339449541284404, "grad_norm": 1.3867453336715698, "learning_rate": 0.000196328029375765, "loss": 1.5433, "step": 60}, {"epoch": 0.0856269113149847, "grad_norm": 1.4274485111236572, "learning_rate": 0.00019571603427172583, "loss": 1.5245, "step": 70}, {"epoch": 0.09785932721712538, "grad_norm": 1.4820953607559204, "learning_rate": 0.00019510403916768668, "loss": 1.4886, "step": 80}, {"epoch": 0.11009174311926606, "grad_norm": 1.307521104812622, "learning_rate": 0.0001944920440636475, "loss": 1.4912, "step": 90}, {"epoch": 0.12232415902140673, "grad_norm": 1.3592833280563354, "learning_rate": 0.00019388004895960835, "loss": 1.5026, "step": 100}, {"epoch": 0.1345565749235474, "grad_norm": 1.3210376501083374, "learning_rate": 0.00019326805385556917, "loss": 1.4934, "step": 110}, {"epoch": 0.14678899082568808, "grad_norm": 1.4047718048095703, "learning_rate": 0.00019265605875153, "loss": 1.5085, "step": 120}, {"epoch": 0.15902140672782875, "grad_norm": 1.3200632333755493, "learning_rate": 0.00019204406364749084, "loss": 1.497, "step": 130}, {"epoch": 0.1712538226299694, "grad_norm": 1.3976047039031982, "learning_rate": 0.00019143206854345166, "loss": 1.482, "step": 140}, {"epoch": 0.1834862385321101, "grad_norm": 1.4371339082717896, "learning_rate": 0.00019082007343941248, "loss": 1.475, "step": 150}, {"epoch": 0.19571865443425077, "grad_norm": 1.5013028383255005, "learning_rate": 0.00019020807833537332, "loss": 1.4604, "step": 160}, {"epoch": 0.20795107033639143, "grad_norm": 1.446024775505066, "learning_rate": 0.00018959608323133414, "loss": 1.4884, "step": 170}, {"epoch": 0.22018348623853212, "grad_norm": 1.443379521369934, "learning_rate": 0.000188984088127295, "loss": 1.4644, "step": 180}, {"epoch": 0.2324159021406728, "grad_norm": 1.360926866531372, "learning_rate": 0.00018837209302325584, "loss": 1.505, "step": 190}, {"epoch": 0.24464831804281345, "grad_norm": 1.4278929233551025, "learning_rate": 0.00018776009791921666, "loss": 1.4265, "step": 200}, {"epoch": 0.25688073394495414, "grad_norm": 1.340205192565918, "learning_rate": 0.0001871481028151775, "loss": 1.4568, "step": 210}, {"epoch": 0.2691131498470948, "grad_norm": 1.5937830209732056, "learning_rate": 0.00018653610771113833, "loss": 1.4805, "step": 220}, {"epoch": 0.28134556574923547, "grad_norm": 1.3773781061172485, "learning_rate": 0.00018592411260709915, "loss": 1.4635, "step": 230}, {"epoch": 0.29357798165137616, "grad_norm": 1.452172875404358, "learning_rate": 0.00018531211750306, "loss": 1.4527, "step": 240}, {"epoch": 0.3058103975535168, "grad_norm": 1.4557768106460571, "learning_rate": 0.0001847001223990208, "loss": 1.4464, "step": 250}, {"epoch": 0.3180428134556575, "grad_norm": 1.281391978263855, "learning_rate": 0.00018408812729498163, "loss": 1.478, "step": 260}, {"epoch": 0.3302752293577982, "grad_norm": 1.5213923454284668, "learning_rate": 0.00018347613219094248, "loss": 1.4346, "step": 270}, {"epoch": 0.3425076452599388, "grad_norm": 1.5025392770767212, "learning_rate": 0.0001828641370869033, "loss": 1.458, "step": 280}, {"epoch": 0.3547400611620795, "grad_norm": 1.3972201347351074, "learning_rate": 0.00018225214198286415, "loss": 1.4623, "step": 290}, {"epoch": 0.3669724770642202, "grad_norm": 1.3648805618286133, "learning_rate": 0.00018164014687882497, "loss": 1.4581, "step": 300}], "logging_steps": 10, "max_steps": 3268, "num_input_tokens_seen": 0, "num_train_epochs": 4, "save_steps": 100, "stateful_callbacks": {"TrainerControl": {"args": {"should_epoch_stop": false, "should_evaluate": false, "should_log": false, "should_save": true, "should_training_stop": false}, "attributes": {}}}, "total_flos": 1.148322619293696e+17, "train_batch_size": 16, "trial_name": null, "trial_params": null}