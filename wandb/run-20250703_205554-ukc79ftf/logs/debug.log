2025-07-03 20:55:54,346 INFO    MainThread:2708485 [wandb_setup.py:_flush():81] Current SDK version is 0.20.1
2025-07-03 20:55:54,347 INFO    MainThread:2708485 [wandb_setup.py:_flush():81] Configure stats pid to 2708485
2025-07-03 20:55:54,347 INFO    MainThread:2708485 [wandb_setup.py:_flush():81] Loading settings from /home/<USER>/m/maryam.hashemzadeh/.config/wandb/settings
2025-07-03 20:55:54,347 INFO    MainThread:2708485 [wandb_setup.py:_flush():81] Loading settings from /network/scratch/m/maryam.hashemzadeh/saftly/mergoo/wandb/settings
2025-07-03 20:55:54,347 INFO    MainThread:2708485 [wandb_setup.py:_flush():81] Loading settings from environment variables
2025-07-03 20:55:54,348 INFO    MainThread:2708485 [wandb_init.py:setup_run_log_directory():703] Logging user logs to /network/scratch/m/maryam.hashemzadeh/saftly/mergoo/wandb/run-20250703_205554-ukc79ftf/logs/debug.log
2025-07-03 20:55:54,348 INFO    MainThread:2708485 [wandb_init.py:setup_run_log_directory():704] Logging internal logs to /network/scratch/m/maryam.hashemzadeh/saftly/mergoo/wandb/run-20250703_205554-ukc79ftf/logs/debug-internal.log
2025-07-03 20:55:54,348 INFO    MainThread:2708485 [wandb_init.py:init():831] calling init triggers
2025-07-03 20:55:54,348 INFO    MainThread:2708485 [wandb_init.py:init():836] wandb.init called with sweep_config: {}
config: {'model_id': 'mistralai/Mistral-7B-v0.1', 'lora_r': 32, 'lora_alpha': 64, 'lora_dropout': 0.1, 'batch_size': 4, 'epochs': 10, 'lr_lora2': 1e-05, 'lr_discriminator': 5e-05, 'weight_decay': 0.01, 'gradient_clip_norm': 1.0, 'adversarial_weight': 0.1, 'max_eval_samples': 5, 'dataset_path': '/home/<USER>/m/maryam.hashemzadeh/scratch/saftly/MoE-PEFT/datasets/beavertail/4_cleaned_330k_train_beaverTails_safe_gpt4_qtype4_drug_abuse_weapons_banned_substance.json', '_wandb': {}}
2025-07-03 20:55:54,348 INFO    MainThread:2708485 [wandb_init.py:init():872] starting backend
2025-07-03 20:55:54,568 INFO    MainThread:2708485 [wandb_init.py:init():875] sending inform_init request
2025-07-03 20:55:54,574 INFO    MainThread:2708485 [wandb_init.py:init():883] backend started and connected
2025-07-03 20:55:54,576 INFO    MainThread:2708485 [wandb_init.py:init():956] updated telemetry
2025-07-03 20:55:54,605 INFO    MainThread:2708485 [wandb_init.py:init():980] communicating run to backend with 90.0 second timeout
2025-07-03 20:55:54,985 INFO    MainThread:2708485 [wandb_init.py:init():1032] starting run threads in backend
2025-07-03 20:55:55,227 INFO    MainThread:2708485 [wandb_run.py:_console_start():2453] atexit reg
2025-07-03 20:55:55,227 INFO    MainThread:2708485 [wandb_run.py:_redirect():2301] redirect: wrap_raw
2025-07-03 20:55:55,227 INFO    MainThread:2708485 [wandb_run.py:_redirect():2370] Wrapping output streams.
2025-07-03 20:55:55,228 INFO    MainThread:2708485 [wandb_run.py:_redirect():2393] Redirects installed.
2025-07-03 20:55:55,232 INFO    MainThread:2708485 [wandb_init.py:init():1078] run started, returning control to user process
