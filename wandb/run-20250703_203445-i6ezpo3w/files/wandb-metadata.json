{"os": "Linux-5.15.0-101-generic-x86_64-with-glibc2.35", "python": "CPython 3.10.11", "startedAt": "2025-07-04T00:34:45.969176Z", "program": "/home/<USER>/m/maryam.hashemzadeh/scratch/saftly/mergoo/project_2/lora_2_train.py", "git": {"remote": "**************:Leeroo-AI/mergoo.git", "commit": "8dec73f1083d21e174484b5b8bc75747c4034282"}, "email": "<EMAIL>", "root": "/network/scratch/m/maryam.hashemzadeh/saftly/mergoo", "host": "cn-g018.server.mila.quebec", "executable": "/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/bin/python3.10", "cpu_count": 64, "cpu_count_logical": 64, "gpu": "NVIDIA A100-SXM4-80GB", "gpu_count": 4, "disk": {"/": {"total": "************", "used": "17487347712"}}, "memory": {"total": "1076141576192"}, "cpu": {"count": 64, "countLogical": 64}, "gpu_nvidia": [{"name": "NVIDIA A100-SXM4-80GB", "memoryTotal": "85899345920", "cudaCores": 6912, "architecture": "Ampere", "uuid": "GPU-52c13542-8b5a-ffb9-22c6-e44c22fbff28"}, {"name": "NVIDIA A100-SXM4-80GB", "memoryTotal": "85899345920", "cudaCores": 6912, "architecture": "Ampere", "uuid": "GPU-e6324f00-b5ee-9530-5de1-f4c60916c5df"}, {"name": "NVIDIA A100-SXM4-80GB", "memoryTotal": "85899345920", "cudaCores": 6912, "architecture": "Ampere", "uuid": "GPU-4db69579-3309-8f45-1a1e-b1387d0105c1"}, {"name": "NVIDIA A100-SXM4-80GB", "memoryTotal": "85899345920", "cudaCores": 6912, "architecture": "Ampere", "uuid": "GPU-26c0c8dd-26aa-eea5-3e5a-b49947912409"}], "slurm": {"job_id": "7113010"}, "cudaVersion": "12.2"}