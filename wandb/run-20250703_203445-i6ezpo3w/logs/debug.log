2025-07-03 20:34:45,732 INFO    MainThread:2701961 [wandb_setup.py:_flush():81] Current SDK version is 0.20.1
2025-07-03 20:34:45,733 INFO    MainThread:2701961 [wandb_setup.py:_flush():81] Configure stats pid to 2701961
2025-07-03 20:34:45,733 INFO    MainThread:2701961 [wandb_setup.py:_flush():81] Loading settings from /home/<USER>/m/maryam.hashemzadeh/.config/wandb/settings
2025-07-03 20:34:45,733 INFO    MainThread:2701961 [wandb_setup.py:_flush():81] Loading settings from /network/scratch/m/maryam.hashemzadeh/saftly/mergoo/wandb/settings
2025-07-03 20:34:45,734 INFO    MainThread:2701961 [wandb_setup.py:_flush():81] Loading settings from environment variables
2025-07-03 20:34:45,734 INFO    MainThread:2701961 [wandb_init.py:setup_run_log_directory():703] Logging user logs to /network/scratch/m/maryam.hashemzadeh/saftly/mergoo/wandb/run-20250703_203445-i6ezpo3w/logs/debug.log
2025-07-03 20:34:45,734 INFO    MainThread:2701961 [wandb_init.py:setup_run_log_directory():704] Logging internal logs to /network/scratch/m/maryam.hashemzadeh/saftly/mergoo/wandb/run-20250703_203445-i6ezpo3w/logs/debug-internal.log
2025-07-03 20:34:45,734 INFO    MainThread:2701961 [wandb_init.py:init():831] calling init triggers
2025-07-03 20:34:45,735 INFO    MainThread:2701961 [wandb_init.py:init():836] wandb.init called with sweep_config: {}
config: {'model_id': 'mistralai/Mistral-7B-v0.1', 'lora_r': 32, 'lora_alpha': 64, 'lora_dropout': 0.1, 'batch_size': 4, 'epochs': 10, 'lr_lora2': 5e-05, 'lr_discriminator': 0.0001, 'adversarial_weight': 0.1, 'max_eval_samples': 5, 'dataset_path': '/home/<USER>/m/maryam.hashemzadeh/scratch/saftly/MoE-PEFT/datasets/beavertail/4_cleaned_330k_train_beaverTails_safe_gpt4_qtype4_drug_abuse_weapons_banned_substance.json', '_wandb': {}}
2025-07-03 20:34:45,735 INFO    MainThread:2701961 [wandb_init.py:init():872] starting backend
2025-07-03 20:34:45,959 INFO    MainThread:2701961 [wandb_init.py:init():875] sending inform_init request
2025-07-03 20:34:45,968 INFO    MainThread:2701961 [wandb_init.py:init():883] backend started and connected
2025-07-03 20:34:45,971 INFO    MainThread:2701961 [wandb_init.py:init():956] updated telemetry
2025-07-03 20:34:45,999 INFO    MainThread:2701961 [wandb_init.py:init():980] communicating run to backend with 90.0 second timeout
2025-07-03 20:34:46,449 INFO    MainThread:2701961 [wandb_init.py:init():1032] starting run threads in backend
2025-07-03 20:34:46,690 INFO    MainThread:2701961 [wandb_run.py:_console_start():2453] atexit reg
2025-07-03 20:34:46,691 INFO    MainThread:2701961 [wandb_run.py:_redirect():2301] redirect: wrap_raw
2025-07-03 20:34:46,691 INFO    MainThread:2701961 [wandb_run.py:_redirect():2370] Wrapping output streams.
2025-07-03 20:34:46,691 INFO    MainThread:2701961 [wandb_run.py:_redirect():2393] Redirects installed.
2025-07-03 20:34:46,697 INFO    MainThread:2701961 [wandb_init.py:init():1078] run started, returning control to user process
2025-07-03 20:35:03,864 INFO    MsgRouterThr:2701961 [mailbox.py:close():129] [no run ID] Closing mailbox, abandoning 1 handles.
